import { Controller, Get } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { DatabaseService } from '../../core/database/database.service';
import { DiscordService } from '../../discord/discord.service';

@ApiTags('health')
@Controller('health')
export class HealthController {
  constructor(
    private discordService: DiscordService,
    private databaseService: DatabaseService,
  ) {}

  @Get()
  @ApiOperation({ summary: 'Get application health status' })
  @ApiResponse({ status: 200, description: 'Health check results' })
  async check() {
    const dbHealthy = await this.databaseService.healthCheck();
    const discordHealth = await this.discordService.healthCheck();
    
    return {
      status: dbHealthy && discordHealth.status === 'healthy' ? 'ok' : 'error',
      timestamp: new Date().toISOString(),
      info: {
        database: { status: dbHealthy ? 'up' : 'down' },
        discord: discordHealth,
      },
      details: {
        database: { status: dbHealthy ? 'up' : 'down' },
        discord: discordHealth,
      },
    };
  }

  @Get('simple')
  @ApiOperation({ summary: 'Simple health check endpoint' })
  @ApiResponse({ status: 200, description: 'Simple health status' })
  async simpleCheck() {
    const dbHealthy = await this.databaseService.healthCheck();
    const discordHealth = await this.discordService.healthCheck();

    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      service: 'discord-bot-nestjs',
      version: '1.0.0',
      uptime: process.uptime(),
      environment: process.env.NODE_ENV || 'development',
      port: process.env.PORT || '8080',
      database: dbHealthy ? 'configured' : 'error',
      discord_token: process.env.DISCORD_TOKEN ? 'configured' : 'missing',
      discord_status: discordHealth.status,
      discord_guilds: discordHealth.guilds,
    };
  }

  @Get('ready')
  @ApiOperation({ summary: 'Deployment readiness check - lightweight for load balancers' })
  @ApiResponse({ status: 200, description: 'Service is ready to accept traffic' })
  async readinessCheck() {
    // This endpoint is designed for deployment health checks
    // It only checks if the service is running and can respond
    // It doesn't require Discord or database to be fully ready

    try {
      const startTime = Date.now();

      // Basic service health - just check if we can respond
      const response = {
        status: 'ready',
        timestamp: new Date().toISOString(),
        service: 'discord-bot-nestjs',
        version: '1.0.0',
        uptime: Math.floor(process.uptime()),
        environment: process.env.NODE_ENV || 'development',
        port: process.env.PORT || '8080',
        host: process.env.HOST || '0.0.0.0',
        responseTime: Date.now() - startTime,
        pid: process.pid,
        memory: {
          used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
          total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024),
        },
        // Configuration checks (non-blocking)
        config: {
          discord_token: !!process.env.DISCORD_TOKEN,
          database_url: !!process.env.DATABASE_URL,
          required_env_vars: this.checkRequiredEnvVars(),
        }
      };

      return response;
    } catch (error) {
      // Even if there's an error, return a response to show the service is running
      return {
        status: 'degraded',
        timestamp: new Date().toISOString(),
        service: 'discord-bot-nestjs',
        error: 'Health check encountered an error but service is responding',
        uptime: Math.floor(process.uptime()),
        pid: process.pid,
      };
    }
  }

  private checkRequiredEnvVars(): boolean {
    const required = ['DISCORD_TOKEN', 'DATABASE_URL', 'PORT'];
    return required.every(env => !!process.env[env]);
  }
}