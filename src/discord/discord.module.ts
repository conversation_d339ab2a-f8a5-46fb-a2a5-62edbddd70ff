import { Module } from '@nestjs/common';
import { DatabaseModule } from '../core/database/database.module';
import { SecurityModule } from '../core/security/security.module';
import { DiscordCommandsService } from './commands/discord-commands.service';
import { DiscordService } from './discord.service';
import { DiscordEventsService } from './events/discord-events.service';
import { DiscordUtilsService } from './utils/discord-utils.service';

@Module({
  imports: [SecurityModule, DatabaseModule],
  providers: [
    DiscordService,
    // Only include Discord bot services if token is provided
    ...(process.env.DISCORD_TOKEN ? [
      DiscordEventsService,
      DiscordCommandsService,
    ] : []),
    DiscordUtilsService,
  ],
  exports: [DiscordService, DiscordUtilsService],
})
export class DiscordModule {}