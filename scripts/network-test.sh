#!/bin/bash

# Network Connectivity Test Script
# Tests port binding and connectivity issues

set -euo pipefail

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${BLUE}[TEST]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

success() {
    echo -e "${GREEN}[OK]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

# Function to test port binding
test_port_binding() {
    local port=${PORT:-8080}
    log "Testing port binding on $port..."
    
    # Check if port is already in use
    if command -v netstat >/dev/null 2>&1; then
        if netstat -tuln | grep -q ":$port "; then
            error "Port $port is already in use:"
            netstat -tuln | grep ":$port "
            return 1
        fi
    elif command -v ss >/dev/null 2>&1; then
        if ss -tuln | grep -q ":$port "; then
            error "Port $port is already in use:"
            ss -tuln | grep ":$port "
            return 1
        fi
    fi
    
    success "Port $port is available"
    return 0
}

# Function to test application startup
test_app_startup() {
    local port=${PORT:-8080}
    log "Testing application startup..."
    
    if [[ ! -f "dist/src/main.js" ]]; then
        error "Application build not found. Run 'pnpm run build' first."
        return 1
    fi
    
    # Start app in background
    log "Starting application in test mode..."
    node dist/src/main.js &
    local pid=$!
    
    # Wait for startup
    local max_wait=30
    local wait_time=0
    
    while [[ $wait_time -lt $max_wait ]]; do
        if ps -p $pid > /dev/null 2>&1; then
            # Check if port is listening
            if command -v netstat >/dev/null 2>&1; then
                if netstat -tuln | grep -q ":$port "; then
                    success "Application is listening on port $port"
                    break
                fi
            elif command -v ss >/dev/null 2>&1; then
                if ss -tuln | grep -q ":$port "; then
                    success "Application is listening on port $port"
                    break
                fi
            fi
        else
            error "Application process died"
            return 1
        fi
        
        sleep 1
        ((wait_time++))
    done
    
    if [[ $wait_time -ge $max_wait ]]; then
        error "Application did not start listening within $max_wait seconds"
        kill $pid 2>/dev/null || true
        return 1
    fi
    
    # Test health endpoint
    log "Testing health endpoint..."
    local health_attempts=0
    local max_health_attempts=10
    
    while [[ $health_attempts -lt $max_health_attempts ]]; do
        if curl -f "http://localhost:$port/api/health" >/dev/null 2>&1; then
            success "Health endpoint is responding"
            break
        fi
        
        sleep 2
        ((health_attempts++))
    done
    
    if [[ $health_attempts -ge $max_health_attempts ]]; then
        warning "Health endpoint not responding (this might be normal during startup)"
    fi
    
    # Test basic connectivity
    log "Testing basic connectivity..."
    if curl -s "http://localhost:$port" >/dev/null 2>&1; then
        success "Basic HTTP connectivity working"
    else
        warning "Basic HTTP connectivity failed"
    fi
    
    # Clean up
    log "Stopping test application..."
    kill $pid 2>/dev/null || true
    wait $pid 2>/dev/null || true
    
    success "Application startup test completed"
    return 0
}

# Function to test external connectivity
test_external_connectivity() {
    local port=${PORT:-8080}
    log "Testing external connectivity..."
    
    # Test if we can bind to 0.0.0.0
    if command -v nc >/dev/null 2>&1; then
        log "Testing 0.0.0.0 binding with netcat..."
        if timeout 5s nc -l 0.0.0.0 $port </dev/null >/dev/null 2>&1 &
        then
            local nc_pid=$!
            sleep 1
            
            if ps -p $nc_pid > /dev/null 2>&1; then
                success "Can bind to 0.0.0.0:$port"
                kill $nc_pid 2>/dev/null || true
            else
                error "Cannot bind to 0.0.0.0:$port"
                return 1
            fi
        fi
    else
        warning "netcat not available, skipping 0.0.0.0 binding test"
    fi
    
    return 0
}

# Function to show network configuration
show_network_config() {
    log "Network Configuration:"
    
    echo "Environment Variables:"
    echo "  PORT: ${PORT:-8080}"
    echo "  HOST: ${HOST:-0.0.0.0}"
    echo "  NODE_ENV: ${NODE_ENV:-development}"
    echo ""
    
    echo "Network Interfaces:"
    if command -v ip >/dev/null 2>&1; then
        ip addr show | grep -E "inet " | head -5
    elif command -v ifconfig >/dev/null 2>&1; then
        ifconfig | grep -E "inet " | head -5
    else
        echo "  Network interface tools not available"
    fi
    echo ""
    
    echo "Listening Ports:"
    if command -v netstat >/dev/null 2>&1; then
        netstat -tuln | head -10
    elif command -v ss >/dev/null 2>&1; then
        ss -tuln | head -10
    else
        echo "  Port listing tools not available"
    fi
    echo ""
}

# Function to test Docker environment
test_docker_environment() {
    log "Testing Docker environment..."
    
    if [[ -f "/.dockerenv" ]]; then
        success "Running inside Docker container"
        
        # Check if we're running as the correct user
        local current_user=$(whoami)
        echo "  Current user: $current_user"
        
        # Check container networking
        if [[ -f "/proc/net/route" ]]; then
            echo "  Container has network access"
        else
            warning "  Container network configuration unclear"
        fi
    else
        log "Not running in Docker container"
    fi
}

# Main function
main() {
    echo "🔍 Network Connectivity Test"
    echo "============================"
    echo ""
    
    show_network_config
    test_docker_environment
    test_port_binding
    test_external_connectivity
    test_app_startup
    
    echo ""
    echo "🔍 Network test complete!"
    echo ""
    echo "If tests pass but deployment still fails:"
    echo "  1. Check Sevalla/platform firewall settings"
    echo "  2. Verify health check endpoint configuration"
    echo "  3. Check application logs for startup errors"
    echo "  4. Ensure environment variables are set correctly"
}

# Handle interruption
trap 'echo ""; error "Test interrupted"; exit 1' INT TERM

# Run main function
main "$@"
