# Multi-stage Docker build for NestJS Discord Bot Backend
# Stage 1: Dependencies and Build
FROM node:18-alpine AS builder

# Set working directory
WORKDIR /app

# Install pnpm globally
RUN npm install -g pnpm@10.12.4

# Copy package files
COPY package.json pnpm-lock.yaml ./

# Install dependencies with cache mount
RUN --mount=type=cache,id=pnpm,target=/root/.local/share/pnpm/store \
    pnpm install --frozen-lockfile

# Copy source code
COPY . .

# Build the NestJS application
RUN pnpm run build

# Stage 2: Production Runtime
FROM node:18-alpine AS runner

# Install curl for health checks and bash for production script
RUN apk add --no-cache curl bash

# Set working directory
WORKDIR /app

# Install pnpm globally
RUN npm install -g pnpm@10.12.4

# Create non-root user
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nestjs

# Copy package files
COPY package.json pnpm-lock.yaml ./

# Install only production dependencies
RUN --mount=type=cache,id=pnpm,target=/root/.local/share/pnpm/store \
    pnpm install --frozen-lockfile --prod

# Copy built application from builder stage
COPY --from=builder --chown=nestjs:nodejs /app/dist ./dist

# Copy other necessary files
COPY --chown=nestjs:nodejs tsconfig.json ./
COPY --chown=nestjs:nodejs nest-cli.json ./

# Create scripts directory and copy production script
RUN mkdir -p scripts
COPY --chown=nestjs:nodejs scripts/production-start.sh ./scripts/

# Make production script executable (before switching to non-root user)
RUN chmod +x scripts/production-start.sh

# Set environment variables
ENV NODE_ENV=production
ENV PORT=8080
ENV HOST=0.0.0.0

# Expose port
EXPOSE 8080

# Switch to non-root user
USER nestjs

# Health check with longer intervals for Discord bot
HEALTHCHECK --interval=60s --timeout=30s --start-period=30s --retries=5 \
    CMD curl -f http://localhost:8080/api/health || exit 1

# Start the NestJS application with production script
CMD ["./scripts/production-start.sh"]